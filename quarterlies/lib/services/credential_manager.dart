import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:crypto/crypto.dart';

/// Production-ready credential management system that works across all environments
/// - Development: Uses secure local config file
/// - Testing: Uses environment variables or test config
/// - Production: Uses environment variables or secure remote config
class CredentialManager {
  static const FlutterSecureStorage _secureStorage = FlutterSecureStorage();
  static const String _configAssetPath = 'assets/config/credentials.json';
  static const String _secureKeyPrefix = 'quarterlies_secure_';

  // Cache for credentials to avoid repeated reads
  static Map<String, String>? _credentialCache;
  static bool _isInitialized = false;

  /// Initialize the credential manager
  static Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      await _loadCredentials();
      _isInitialized = true;

      if (kDebugMode) {
        debugPrint('✅ CredentialManager initialized successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        debugPrint('❌ CredentialManager initialization failed: $e');
      }
      rethrow;
    }
  }

  /// Get Supabase URL
  static Future<String> getSupabaseUrl() async {
    await _ensureInitialized();
    return _getCredential('SUPABASE_URL') ?? _getDefaultSupabaseUrl();
  }

  /// Get Supabase Anonymous Key
  static Future<String> getSupabaseAnonKey() async {
    await _ensureInitialized();
    return _getCredential('SUPABASE_ANON_KEY') ?? _getDefaultSupabaseAnonKey();
  }

  /// Get Supabase Service Role Key (for server-side operations only)
  static Future<String?> getSupabaseServiceRoleKey() async {
    await _ensureInitialized();
    // Service role key should only be available in secure server environments
    if (kIsWeb || !_isServerEnvironment()) {
      if (kDebugMode) {
        debugPrint('⚠️ Service role key not available in client environment');
      }
      return null;
    }
    return _getCredential('SUPABASE_SERVICE_ROLE_KEY');
  }

  /// Load credentials from various sources in priority order
  static Future<void> _loadCredentials() async {
    _credentialCache = {};

    // Priority 1: Environment variables (production/CI)
    await _loadFromEnvironment();

    // Priority 2: Secure storage (cached from previous runs)
    await _loadFromSecureStorage();

    // Priority 3: Asset file (development)
    await _loadFromAssets();

    // Priority 4: Default values (fallback for development)
    _loadDefaults();

    // Store loaded credentials securely for future use
    await _storeCredentialsSecurely();
  }

  /// Load credentials from environment variables
  static Future<void> _loadFromEnvironment() async {
    // Load each environment variable individually since String.fromEnvironment requires constants
    const supabaseUrl = String.fromEnvironment('SUPABASE_URL');
    const supabaseAnonKey = String.fromEnvironment('SUPABASE_ANON_KEY');
    const supabaseServiceRoleKey = String.fromEnvironment(
      'SUPABASE_SERVICE_ROLE_KEY',
    );

    if (supabaseUrl.isNotEmpty) {
      _credentialCache!['SUPABASE_URL'] = supabaseUrl;
      if (kDebugMode) {
        debugPrint('📝 Loaded SUPABASE_URL from environment');
      }
    }

    if (supabaseAnonKey.isNotEmpty) {
      _credentialCache!['SUPABASE_ANON_KEY'] = supabaseAnonKey;
      if (kDebugMode) {
        debugPrint('📝 Loaded SUPABASE_ANON_KEY from environment');
      }
    }

    if (supabaseServiceRoleKey.isNotEmpty) {
      _credentialCache!['SUPABASE_SERVICE_ROLE_KEY'] = supabaseServiceRoleKey;
      if (kDebugMode) {
        debugPrint('📝 Loaded SUPABASE_SERVICE_ROLE_KEY from environment');
      }
    }
  }

  /// Load credentials from secure storage
  static Future<void> _loadFromSecureStorage() async {
    try {
      final storedCredentials = await _secureStorage.read(
        key: '${_secureKeyPrefix}credentials',
      );

      if (storedCredentials != null) {
        final decoded = jsonDecode(storedCredentials) as Map<String, dynamic>;
        for (final entry in decoded.entries) {
          if (_credentialCache![entry.key] == null) {
            _credentialCache![entry.key] = entry.value.toString();
            if (kDebugMode) {
              debugPrint('🔐 Loaded ${entry.key} from secure storage');
            }
          }
        }
      }
    } catch (e) {
      if (kDebugMode) {
        debugPrint('⚠️ Could not load from secure storage: $e');
      }
    }
  }

  /// Load credentials from assets (development)
  static Future<void> _loadFromAssets() async {
    try {
      final configString = await rootBundle.loadString(_configAssetPath);
      final config = jsonDecode(configString) as Map<String, dynamic>;

      for (final entry in config.entries) {
        if (_credentialCache![entry.key] == null) {
          _credentialCache![entry.key] = entry.value.toString();
          if (kDebugMode) {
            debugPrint('📄 Loaded ${entry.key} from assets');
          }
        }
      }
    } catch (e) {
      if (kDebugMode) {
        debugPrint(
          '📄 No asset config found (this is normal for production): $e',
        );
      }
    }
  }

  /// Load default values (development fallback)
  static void _loadDefaults() {
    final defaults = {
      'SUPABASE_URL': _getDefaultSupabaseUrl(),
      'SUPABASE_ANON_KEY': _getDefaultSupabaseAnonKey(),
    };

    for (final entry in defaults.entries) {
      if (_credentialCache![entry.key] == null) {
        _credentialCache![entry.key] = entry.value;
        if (kDebugMode) {
          debugPrint('🔧 Using default value for ${entry.key}');
        }
      }
    }
  }

  /// Store credentials securely for future use
  static Future<void> _storeCredentialsSecurely() async {
    try {
      // Only store non-sensitive credentials
      final safeCredentials = Map<String, String>.from(_credentialCache!)
        ..remove('SUPABASE_SERVICE_ROLE_KEY'); // Never store service role key

      await _secureStorage.write(
        key: '${_secureKeyPrefix}credentials',
        value: jsonEncode(safeCredentials),
      );
    } catch (e) {
      if (kDebugMode) {
        debugPrint('⚠️ Could not store credentials securely: $e');
      }
    }
  }

  /// Get credential from cache
  static String? _getCredential(String key) {
    return _credentialCache?[key];
  }

  /// Ensure the credential manager is initialized
  static Future<void> _ensureInitialized() async {
    if (!_isInitialized) {
      await initialize();
    }
  }

  /// Check if running in a server environment
  static bool _isServerEnvironment() {
    try {
      // Check for server environment indicators
      return Platform.environment.containsKey('SERVER_ENV') ||
          Platform.environment.containsKey('CI') ||
          Platform.environment.containsKey('GITHUB_ACTIONS');
    } catch (e) {
      // If Platform.environment is not available (e.g., on web), assume client environment
      return false;
    }
  }

  /// Get default Supabase URL
  static String _getDefaultSupabaseUrl() {
    return 'https://hoeagvrddekfmeqqkxca.supabase.co';
  }

  /// Get default Supabase Anonymous Key
  static String _getDefaultSupabaseAnonKey() {
    return 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImhvZWFndnJkZGVrZm1lcXFreGNhIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDYyODMyNTAsImV4cCI6MjA2MTg1OTI1MH0.gU_8hNKC_HwEc-7fCJC6ARyZbDO92lCet6RBfuvx4Yo';
  }

  /// Clear cached credentials (for testing)
  static Future<void> clearCache() async {
    _credentialCache = null;
    _isInitialized = false;
    await _secureStorage.delete(key: '${_secureKeyPrefix}credentials');
  }

  /// Validate that all required credentials are available
  static Future<bool> validateCredentials() async {
    await _ensureInitialized();

    final url = await getSupabaseUrl();
    final anonKey = await getSupabaseAnonKey();

    final isValid =
        url.isNotEmpty &&
        anonKey.isNotEmpty &&
        url.startsWith('https://') &&
        anonKey.contains('.');

    if (kDebugMode) {
      debugPrint(isValid ? '✅ Credentials validated' : '❌ Invalid credentials');
    }

    return isValid;
  }
}
