// This file is for development purposes and should not be committed to version control.
// For production, use a secure method to inject these values (e.g., Flutter build flavors, CI/CD secrets).

import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:flutter/foundation.dart';

class AppConfig {
  static const FlutterSecureStorage _secureStorage = FlutterSecureStorage();

  // Environment variable approach (for production)
  static const String _envSupabaseUrl = String.fromEnvironment(
    'SUPABASE_URL',
    defaultValue: '',
  );
  static const String _envSupabaseAnonKey = String.fromEnvironment(
    'SUPABASE_ANON_KEY',
    defaultValue: '',
  );
  static const String _envSupabaseServiceRoleKey = String.fromEnvironment(
    'SUPABASE_SERVICE_ROLE_KEY',
    defaultValue: '',
  );

  // Development fallback values (will be stored securely)
  static const String _devSupabaseUrl =
      'https://hoeagvrddekfmeqqkxca.supabase.co';
  static const String _devSupabaseAnonKey =
      'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImhvZWFndnJkZGVrZm1lcXFreGNhIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDYyODMyNTAsImV4cCI6MjA2MTg1OTI1MH0.gU_8hNKC_HwEc-7fCJC6ARyZbDO92lCet6RBfuvx4Yo';
  static const String _devSupabaseServiceRoleKey =
      'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImhvZWFndnJkZGVrZm1lcXFreGNhIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NjI4MzI1MCwiZXhwIjoyMDYxODU5MjUwfQ.u_AmabaIzNdbBLlpj3rh_7tYQTvdL5-7HXKuEXSlsDo';

  // Public getters that prioritize environment variables, then fall back to development values
  static String get supabaseUrl {
    if (_envSupabaseUrl.isNotEmpty) {
      return _envSupabaseUrl;
    }
    return _devSupabaseUrl;
  }

  static String get supabaseAnonKey {
    if (_envSupabaseAnonKey.isNotEmpty) {
      return _envSupabaseAnonKey;
    }
    return _devSupabaseAnonKey;
  }

  static String get supabaseServiceRoleKey {
    if (_envSupabaseServiceRoleKey.isNotEmpty) {
      return _envSupabaseServiceRoleKey;
    }
    return _devSupabaseServiceRoleKey;
  }

  // Initialize secure storage of credentials (for enhanced security)
  static Future<void> initializeSecureConfig() async {
    try {
      // Store credentials securely for future use
      await _secureStorage.write(key: 'supabase_url', value: supabaseUrl);
      await _secureStorage.write(
        key: 'supabase_anon_key',
        value: supabaseAnonKey,
      );
      await _secureStorage.write(
        key: 'supabase_service_role_key',
        value: supabaseServiceRoleKey,
      );
    } catch (e) {
      // Fail silently in case secure storage is not available (e.g., on web)
      if (kDebugMode) {
        debugPrint('Warning: Could not initialize secure config storage: $e');
      }
    }
  }

  // Retrieve credentials from secure storage (fallback method)
  static Future<String?> getSecureSupabaseUrl() async {
    try {
      return await _secureStorage.read(key: 'supabase_url');
    } catch (e) {
      return null;
    }
  }

  static Future<String?> getSecureSupabaseAnonKey() async {
    try {
      return await _secureStorage.read(key: 'supabase_anon_key');
    } catch (e) {
      return null;
    }
  }
}
